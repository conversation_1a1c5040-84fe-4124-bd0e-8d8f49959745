import {
  http
} from '@/common/request/index.js'; // 局部引入

const api = {
  getAgentList: '/bdm/agent/page',
  addAgent: '/bdm/agent/add',
  editAgent: '/bdm/agent/edit',
  deleteAgent: '/bdm/agent/delete',
  info: '/bdm/agent/info',
  getProvince: '/magic-api/common/getProvince',
  getCityByProvinceCode: '/magic-api/common/getCityByProvinceCode',
}

// 获取代理商列表
export const getAgentList = (params) => {
  // 模拟API调用
  return http.get(api.getAgentList, { params });
}

// 新增代理商
export const addAgent = (data) => {
  return http.post(api.addAgent, data);
}

// 编辑代理商
export const editAgent = (data) => {
  return http.post(api.editAgent, data);
}

// 删除代理商
export const deleteAgent = (data) => {
  return http.post(api.deleteAgent, data);
}

// 获取代理商详情
export const getAgentDetail = (params) => {
  return http.get(api.getAgentDetail, params);
}

