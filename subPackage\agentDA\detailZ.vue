<template>
  <view class="detail-page">
    <scroll-view class="content" scroll-y>
      <!-- 基本信息 -->
      <view class="info-card">
        <view class="card-header">
          <view class="header-icon">
            <uv-icon
              name="info-circle-fill"
              size="20"
              color="#52acff"
            ></uv-icon>
          </view>
          <text class="header-title">基本信息</text>
        </view>
        <view class="card-content">
          <view class="info-row">
            <text class="info-label">代理商名称</text>
            <text class="info-value">{{
              agentInfo.agentName || "深圳市腾讯计算机系统有限公司"
            }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">代理商编号</text>
            <text class="info-value">{{
              agentInfo.agentCode || "PDD20240001"
            }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">代理商类型</text>
            <view class="info-value">
              <view class="type-tags">
                <van-tag
                  v-if="agentInfo.agentType === 'enterprise'"
                  type="primary"
                  >企业</van-tag
                >
                <van-tag v-else type="success">个人</van-tag>
              </view>
            </view>
          </view>
          <view class="info-row">
            <text class="info-label">证件信息</text>
            <text class="info-value">{{
              agentInfo.certificateType || "营业执照"
            }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">证件编号</text>
            <text class="info-value">{{
              agentInfo.certificateNumber || "91440300715267260G"
            }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">合作状态</text>
            <view class="info-value">
              <van-tag
                v-if="agentInfo.agentType === 'enterprise'"
                type="primary"
                >企业</van-tag
              >
              <van-tag v-else type="success">个人</van-tag>
            </view>
          </view>
        </view>
      </view>

      <!-- 联系人信息 -->
      <view class="info-card">
        <view class="card-header">
          <view class="header-icon">
            <uv-icon name="account-fill" size="20" color="#52acff"></uv-icon>
          </view>
          <text class="header-title">联系人信息</text>
        </view>
        <view class="card-content">
          <view class="info-row">
            <text class="info-label">联系人姓名</text>
            <text class="info-value">{{
              agentInfo.contactName || "深圳市腾讯计算机系统有限公司"
            }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">身份证号</text>
            <text class="info-value">{{
              agentInfo.idNumber || "PDD20240001"
            }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">联系电话</text>
            <text class="info-value">{{
              agentInfo.phone || "**************"
            }}</text>
          </view>
        </view>
      </view>

      <!-- 地址信息 -->
      <view class="info-card">
        <view class="card-header">
          <view class="header-icon">
            <uv-icon name="map-pin-fill" size="20" color="#52acff"></uv-icon>
          </view>
          <text class="header-title">地址信息</text>
        </view>
        <view class="card-content">
          <view class="info-row">
            <text class="info-label">所在地区</text>
            <text class="info-value">{{
              agentInfo.region || "广东省 深圳市 南山区"
            }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">详细地址</text>
            <text class="info-value">{{
              agentInfo.address || "科技园科技中一路腾讯大厦"
            }}</text>
          </view>
        </view>
      </view>

      <!-- 业务覆盖 -->
      <view class="info-card">
        <view class="card-header">
          <view class="header-icon">
            <uv-icon name="grid-fill" size="20" color="#52acff"></uv-icon>
          </view>
          <text class="header-title">业务覆盖</text>
        </view>
        <view class="card-content">
          <view class="info-row">
            <text class="info-label">覆盖省份/区域</text>
            <text class="info-value">{{
              agentInfo.coverageArea || "深圳市腾讯计算机系统有限公司"
            }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">人员数量</text>
            <text class="info-value">{{
              agentInfo.staffCount || "100人以上"
            }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">财税能力</text>
            <text class="info-value">{{
              agentInfo.taxCapability || "强"
            }}</text>
          </view>
        </view>
      </view>

      <!-- 附件预览 -->
      <view class="info-card">
        <view class="card-header">
          <view class="header-icon">
            <uv-icon name="file-text-fill" size="20" color="#52acff"></uv-icon>
          </view>
          <text class="header-title">附件预览</text>
        </view>
        <view class="card-content">
          <view class="attachment-section">
            <text class="attachment-label">营业执照</text>
            <view class="attachment-preview">
              <image
                :src="
                  agentInfo.businessLicense || '/static/image/bgs/empty.png'
                "
                class="license-image"
                mode="aspectFit"
                @click="previewImage"
              ></image>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { getAgentDetail } from "@/common/api/agentDA/index.js";

export default {
  data() {
    return {
      agentId: "",
      agentInfo: {},
    };
  },

  onLoad(options) {
    if (options.id) {
      this.agentId = options.id;
      this.loadAgentDetail();
    }
  },

  methods: {
    // 加载代理商详情
    async loadAgentDetail() {
      try {
        uni.showLoading({
          title: "加载中...",
        });

        const res = await getAgentDetail(this.agentId);

        uni.hideLoading();

        if (res.code === 200) {
          this.agentInfo = res.data;
        } else {
          uni.showToast({
            title: res.message || "加载失败",
            icon: "none",
          });
        }
      } catch (error) {
        uni.hideLoading();
        console.error("加载代理商详情失败:", error);
        uni.showToast({
          title: "网络错误",
          icon: "none",
        });
      }
    },

    // 预览图片
    previewImage() {
      const imageUrl =
        this.agentInfo.businessLicense || "/static/image/bgs/empty.png";
      uni.previewImage({
        urls: [imageUrl],
        current: imageUrl,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/static/scss/global.scss";

.detail-page {
  min-height: 100vh;
  padding-bottom: env(safe-area-inset-bottom);
}

.content {
  padding: 24rpx;
}

.info-card {
  @include cardBgCommonStyle();
  margin-bottom: 24rpx;
  overflow: hidden;

  &:last-child {
    margin-bottom: 0;
  }
}

.card-header {
  display: flex;
  align-items: center;
  padding: 32rpx 32rpx 24rpx 32rpx;
  background: linear-gradient(
    135deg,
    rgba(82, 172, 255, 0.08) 0%,
    rgba(82, 172, 255, 0.04) 100%
  );
  border-bottom: 2rpx solid rgba(82, 172, 255, 0.1);

  .header-icon {
    margin-right: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48rpx;
    height: 48rpx;
    background: rgba(82, 172, 255, 0.1);
    border-radius: 50%;
  }

  .header-title {
    @include setBoldFont(32rpx, 40rpx, #333);
    flex: 1;
  }
}

.card-content {
  padding: 0;
}

.info-row {
  display: flex;
  align-items: flex-start;
  padding: 28rpx 32rpx;
  border-bottom: 2rpx solid #f8f9fa;
  min-height: 80rpx;

  &:last-child {
    border-bottom: none;
  }
}

.info-label {
  @include setlightFont(28rpx, 40rpx, #666);
  width: 200rpx;
  flex-shrink: 0;
  margin-right: 24rpx;
}

.info-value {
  @include setlightFont(28rpx, 40rpx, #333);
  flex: 1;
  word-break: break-all;

  .type-tags {
    display: inline-block;
  }
}

.attachment-section {
  padding: 32rpx;
}

.attachment-label {
  @include setBoldFont(28rpx, 40rpx, #333);
  display: block;
  margin-bottom: 24rpx;
}

.attachment-preview {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
  border: 2rpx dashed #e0e0e0;
}

.license-image {
  width: 100%;
  max-width: 600rpx;
  height: 400rpx;
  border-radius: 8rpx;
  background: #fff;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.02);
  }
}

// 标签样式覆盖
:deep(.uv-tags) {
  border-radius: 8rpx !important;
  font-weight: 500 !important;
}

:deep(.uv-tags--primary) {
  background: linear-gradient(135deg, #52acff 0%, #4a9eff 100%) !important;
  color: #fff !important;
}

:deep(.uv-tags--success) {
  background: linear-gradient(135deg, #52c41a 0%, #47b818 100%) !important;
  color: #fff !important;
}
</style>
